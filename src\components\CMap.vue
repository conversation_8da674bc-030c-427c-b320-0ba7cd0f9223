<template>
  <div id="map-container">
    <!-- 地图类型切换按钮 -->
    <div class="map-controls">
      <button :class="['map-btn', { active: currentMapType === 'satellite' }]" @click="switchMapType('satellite')">
        影像地图
      </button>
      <button :class="['map-btn', { active: currentMapType === 'vector' }]" @click="switchMapType('vector')">
        电子地图
      </button>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import mapboxgl from 'mapbox-gl'
import 'mapbox-gl/dist/mapbox-gl.css'

// 响应式数据
const currentMapType = ref('satellite') // 当前地图类型：satellite(影像) 或 vector(电子)
let map = null // 地图实例

// 地图切换函数
const switchMapType = (mapType) => {
  if (!map || currentMapType.value === mapType) return

  currentMapType.value = mapType

  if (mapType === 'satellite') {
    // 显示影像地图图层
    map.setLayoutProperty('影像图层1', 'visibility', 'visible')
    map.setLayoutProperty('影像图层2', 'visibility', 'visible')
    // 隐藏电子地图图层
    map.setLayoutProperty('电子图层1', 'visibility', 'none')
    map.setLayoutProperty('电子图层2', 'visibility', 'none')
  } else {
    // 隐藏影像地图图层
    map.setLayoutProperty('影像图层1', 'visibility', 'none')
    map.setLayoutProperty('影像图层2', 'visibility', 'none')
    // 显示电子地图图层
    map.setLayoutProperty('电子图层1', 'visibility', 'visible')
    map.setLayoutProperty('电子图层2', 'visibility', 'visible')
  }
}

// DOM挂载
onMounted(() => {
  mapboxgl.accessToken = 'pk.eyJ1IjoiYTEwNjcxMTE3NTYiLCJhIjoiY2tpenJpeWM1MTc0NjJxbTZnMzFhOWc3eiJ9._sYMrEMP0MT4EDmMGIRAFA'
  mapboxgl.disableTelemetry = true // 关键：禁用遥测

  map = new mapboxgl.Map({
    container: 'map-container',
    style: {
      "version": 8,
      "sources": {},
      "layers": []
    },
    zoom: 10,
    center: [107.037932, 27.72829]
  })

  map.on('load', () => {
    // 添加影像地图资源和图层
    map.addSource('影像底图资源1', {
      type: 'raster',
      tiles: ['http://t0.tianditu.gov.cn/DataServer?T=img_w&x={x}&y={y}&l={z}&tk=bac31128a02d7b76788548a33ac6817b'],
      tileSize: 256
    })
    map.addLayer({
      id: '影像图层1',
      type: 'raster',
      source: '影像底图资源1',
      layout: {
        visibility: 'visible'
      }
    })

    map.addSource('影像底图资源2', {
      type: 'raster',
      tiles: ['https://t1.tianditu.gov.cn/DataServer?T=cia_w&x={x}&y={y}&l={z}&tk=bac31128a02d7b76788548a33ac6817b'],
      tileSize: 256
    })
    map.addLayer({
      id: '影像图层2',
      type: 'raster',
      source: '影像底图资源2',
      layout: {
        visibility: 'visible'
      }
    })

    // 添加电子地图资源和图层
    map.addSource('电子底图资源1', {
      type: 'raster',
      tiles: ['http://t0.tianditu.gov.cn/DataServer?T=vec_w&x={x}&y={y}&l={z}&tk=bac31128a02d7b76788548a33ac6817b'],
      tileSize: 256
    })
    map.addLayer({
      id: '电子图层1',
      type: 'raster',
      source: '电子底图资源1',
      layout: {
        visibility: 'none'
      }
    })

    map.addSource('电子底图资源2', {
      type: 'raster',
      tiles: ['https://t1.tianditu.gov.cn/DataServer?T=cva_w&x={x}&y={y}&l={z}&tk=bac31128a02d7b76788548a33ac6817b'],
      tileSize: 256
    })
    map.addLayer({
      id: '电子图层2',
      type: 'raster',
      source: '电子底图资源2',
      layout: {
        visibility: 'none'
      }
    })
  })
})
</script>

<style scoped>
#map-container {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

.map-controls {
  position: absolute;
  bottom: 4rem;
  right: 31rem;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.map-btn {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.map-btn:hover {
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.map-btn.active {
  background: #409eff;
  color: white;
  border-color: #409eff;
}

.map-btn.active:hover {
  background: #337ecc;
  border-color: #337ecc;
}
</style>
