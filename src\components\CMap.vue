<template>
  <div id="map-container"></div>
  
</template>

<script setup>
import { onMounted } from 'vue'
import mapboxgl from 'mapbox-gl'
 import 'mapbox-gl/dist/mapbox-gl.css'


 //dom
 onMounted(()=>{
  mapboxgl.accessToken='pk.eyJ1IjoiYTEwNjcxMTE3NTYiLCJhIjoiY2tpenJpeWM1MTc0NjJxbTZnMzFhOWc3eiJ9._sYMrEMP0MT4EDmMGIRAFA'
  mapboxgl.disableTelemetry = true; // 关键：禁用遥测
  const map =new mapboxgl.Map({
     container:'map-container',
    style:{
       "version": 8,
        "sources": {},
        "layers": []
    },
    zoom:10,
     center:[107.037932, 27.72829]
  });
  map.on('load',()=>{
    map.addSource('影像底图资源1',{
      type:'raster',//类型：栅格
      tiles:['https://t1.tianditu.gov.cn/DataServer?T=img_w&x={x}&y={y}&l={z}&tk=bac31128a02d7b76788548a33ac6817b'],
      tileSize:256
    });
    map.addLayer({
       id: '影像图层1',
        type: 'raster',
        source: '影像底图资源1',
        visible: true,
    });
    map.addSource('影像底图资源2',{
       type: 'raster',
        tiles: ['https://t1.tianditu.gov.cn/DataServer?T=cia_w&x={x}&y={y}&l={z}&tk=bac31128a02d7b76788548a33ac6817b'],
        tileSize: 256
    });
    map.addLayer({
       id: '影像图层2',
        type: 'raster',
        source: '影像底图资源2',
        visible: true,
    })
  })
})
</script>

<style>
#map-container{
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  
}

</style>

